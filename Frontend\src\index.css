@import "tailwindcss";

@layer base {
  :root {
    --color-bg-primary: 255 255 255; /* white */
    --color-bg-secondary: 243 244 246; /* gray-100 */
    --color-bg-accent: 59 130 246; /* blue-500 */
    --color-text-primary: 17 24 39; /* gray-900 */
    --color-text-secondary: 107 114 128; /* gray-500 */
    --color-text-accent: 59 130 246; /* blue-500 */
    --color-border-primary: 229 231 235; /* gray-200 */
    --color-border-secondary: 209 213 219; /* gray-300 */
  }

  .dark {
    --color-bg-primary: 31 41 55; /* gray-800 */
    --color-bg-secondary: 55 65 81; /* gray-700 */
    --color-bg-accent: 59 130 246; /* blue-500 */
    --color-text-primary: 243 244 246; /* gray-100 */
    --color-text-secondary: 156 163 175; /* gray-400 */
    --color-text-accent: 96 165 250; /* blue-400 */
    --color-border-primary: 75 85 99; /* gray-600 */
    --color-border-secondary: 107 114 128; /* gray-500 */
  }

  body {
    @apply bg-[rgb(var(--color-bg-primary))] text-[rgb(var(--color-text-primary))] transition-colors duration-200;
  }
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: rgb(55 65 81); /* gray-700 */
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99); /* gray-600 */
  border-radius: 5px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128); /* gray-500 */
}
