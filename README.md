# 🌟 LUMINA E-Commerce Store

<div align="center">
  
![LUMINA Logo](https://img.shields.io/badge/LUMINA-E--Commerce-blue?style=for-the-badge&logo=shopping-cart&logoColor=white)

[![MERN Stack](https://img.shields.io/badge/MERN-Stack-green?style=for-the-badge&logo=mongodb&logoColor=white)](https://github.com/your-repo)
[![Razorpay](https://img.shields.io/badge/Razorpay-Integrated-blue?style=for-the-badge&logo=razorpay&logoColor=white)](https://razorpay.com/)
[![MIT License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)](LICENSE)

**🎓 A comprehensive full-stack e-commerce platform built during internship at Euphoria GenX**

[🚀 Live Demo](https://your-demo-link.com) • [📖 Documentation](https://github.com/your-repo/wiki) • [🐛 Report Bug](https://github.com/your-repo/issues) • [✨ Request Feature](https://github.com/your-repo/issues)

</div>

---

## 🎯 **Internship Project for Euphoria GenX**

> 💡 **Project Overview**: This project was completed as part of an intensive internship and training program with **[Euphoria GenX](https://euphoriagenx.com/)**. It demonstrates advanced proficiency in full-stack development using the MERN stack (MongoDB, Express, React, Node.js) with secure payment integration and modern web development practices.

<div align="center">

### 📊 **Project Stats**
| 📝 **Category** | 📈 **Details** |
|----------------|----------------|
| 🏢 **Organization** | Euphoria GenX |
| ⏱️ **Duration** | 3 Months |
| 👨‍💻 **Role** | Full Stack Developer Intern |
| 🛠️ **Tech Stack** | MERN + Razorpay |
| 📋 **Status** | ✅ Completed & Certified |

</div>

## 🚀 Features

<div align="center">
  
### 🛍️ **Complete E-Commerce Experience**

</div>

<details>
<summary>👤 <strong>User Features</strong> - Click to expand</summary>

- **🔐 Authentication System**
  - User registration and login
  - Secure password hashing with bcryptjs
  - JWT token-based authentication
  - Profile management and updates

- **🛒 Shopping Experience**
  - Browse products with advanced search
  - Filter and sort functionality
  - Add to cart with quantity management
  - Wishlist functionality (coming soon)

- **💳 Checkout & Payments**
  - Secure checkout process with validation
  - Razorpay payment gateway integration
  - Order tracking and history
  - Email notifications (planned)

</details>

<details>
<summary>👑 <strong>Admin Features</strong> - Click to expand</summary>

- **📊 Dashboard Management**
  - Comprehensive admin dashboard
  - Real-time statistics and analytics
  - User and order management

- **📦 Product Management**
  - Add, edit, and delete products
  - Image upload with validation
  - Inventory management
  - Bulk operations support

- **🎯 Order Management**
  - View and process orders
  - Update order status
  - Generate reports (planned)

</details>

<details>
<summary>📱 <strong>Technical Features</strong> - Click to expand</summary>

- **🎨 Modern UI/UX**
  - Responsive design for all devices
  - Dark/Light theme support
  - Intuitive navigation
  - Loading states and error handling

- **🔒 Security & Performance**
  - Input validation and sanitization
  - XSS and CSRF protection
  - Rate limiting (planned)
  - Optimized database queries

</details>

## 🛠️ Tech Stack

<div align="center">

### 🎨 **Frontend Technologies**
![React](https://img.shields.io/badge/React-19-61DAFB?style=for-the-badge&logo=react&logoColor=white)
![Vite](https://img.shields.io/badge/Vite-5.0-646CFF?style=for-the-badge&logo=vite&logoColor=white)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.0-06B6D4?style=for-the-badge&logo=tailwindcss&logoColor=white)
![React Router](https://img.shields.io/badge/React_Router-6.0-CA4245?style=for-the-badge&logo=react-router&logoColor=white)

### ⚡ **Backend Technologies**
![Node.js](https://img.shields.io/badge/Node.js-18+-339933?style=for-the-badge&logo=node.js&logoColor=white)
![Express](https://img.shields.io/badge/Express.js-4.x-000000?style=for-the-badge&logo=express&logoColor=white)
![MongoDB](https://img.shields.io/badge/MongoDB-6.0-47A248?style=for-the-badge&logo=mongodb&logoColor=white)
![JWT](https://img.shields.io/badge/JWT-Auth-000000?style=for-the-badge&logo=jsonwebtokens&logoColor=white)

### 💳 **Payment & Tools**
![Razorpay](https://img.shields.io/badge/Razorpay-Payment-blue?style=for-the-badge&logo=razorpay&logoColor=white)
![Postman](https://img.shields.io/badge/Postman-API_Testing-FF6C37?style=for-the-badge&logo=postman&logoColor=white)

</div>

### 🎨 Frontend
| Technology | Purpose | Version |
|------------|---------|---------|
| ⚛️ **React.js** | UI Library | 19.x |
| 🛣️ **React Router** | Navigation | 6.x |
| 🎯 **Context API** | State Management | Built-in |
| 🎨 **Tailwind CSS** | Styling Framework | 3.x |
| 🖼️ **React Icons** | Icon Library | Latest |
| 📡 **Axios** | HTTP Client | Latest |

### ⚡ Backend
| Technology | Purpose | Version |
|------------|---------|---------|
| 🟢 **Node.js** | Runtime Environment | 18+ |
| ⚡ **Express.js** | Web Framework | 4.x |
| 🍃 **MongoDB** | Database | 6.x |
| 🔗 **Mongoose** | ODM | Latest |
| 🔐 **JWT** | Authentication | Latest |
| 📁 **Multer** | File Uploads | Latest |
| 💳 **Razorpay** | Payment Gateway | Latest |

## 🏗️ Project Structure

<div align="center">

### 📁 **Well-Organized Codebase**
*Click on any section below to explore the detailed structure*

</div>

<details>
<summary>🎨 <strong>Frontend Structure</strong> - Modern React Application</summary>

```
Frontend/
├── 📁 public/                    # Static assets
│   ├── 🖼️ icon.png              # App favicon
│   └── ⚡ vite.svg              # Vite logo
├── 📁 src/
│   ├── 🎨 assets/                # Images, fonts, etc.
│   │   └── ⚛️ react.svg
│   ├── 🧩 components/            # Reusable components
│   │   ├── 📦 common/            # Shared UI components
│   │   │   ├── ⏳ LoadingSpinner.jsx
│   │   │   ├── 🌙 ThemeToggle.jsx
│   │   │   └── 🍞 Toast.jsx
│   │   ├── 🏠 layout/            # Layout components
│   │   │   └── 🧭 Navbar.jsx
│   │   └── 🎯 features/          # Feature-specific components
│   │       ├── 📝 AddEditProductPage.jsx
│   │       ├── 📊 AdminOrderDashboard.jsx
│   │       ├── 👤 AdminProfilePage.jsx
│   │       ├── ✅ CheckoutSteps.jsx
│   │       ├── 📄 OrderSummary.jsx
│   │       ├── 💳 PaymentMethodSelector.jsx
│   │       ├── 💰 RazorpayPayment.jsx
│   │       └── 📮 ShippingAddressForm.jsx
│   ├── ⚙️ config/                # Configuration files
│   │   └── 🔧 appConfig.js
│   ├── 📋 constants/             # Constants and enums
│   │   └── 🔗 apiEndpoints.js
│   ├── 🎯 context/               # React context providers
│   │   ├── 🔐 AuthContext.jsx
│   │   ├── 🛒 CartContext.jsx
│   │   ├── 💳 CheckoutContext.jsx
│   │   ├── 🌙 ThemeContext.jsx
│   │   └── 🍞 ToastContext.jsx
│   ├── 🪝 hooks/                 # Custom React hooks
│   │   └── 🔑 useAuth.js
│   ├── 📄 pages/                 # Page components
│   │   ├── 👑 admin/             # Admin pages
│   │   │   └── 📊 DashboardPage.jsx
│   │   ├── 🔐 auth/              # Authentication pages
│   │   │   └── 🚪 LoginRegisterPage.jsx
│   │   ├── 🛒 cart/              # Cart and checkout pages
│   │   │   ├── 🛍️ CartPage.jsx
│   │   │   ├── 💳 PaymentPage.jsx
│   │   │   └── 📮 ShippingPage.jsx
│   │   ├── 📦 product/           # Product pages
│   │   │   ├── 📱 ProductDetailPage.jsx
│   │   │   └── 🏪 ShopPage.jsx
│   │   └── 👤 user/              # User profile pages
│   │       └── 👨‍💼 ProfilePage.jsx
│   ├── 🛣️ routes/                # Route definitions and guards
│   │   ├── 👑 AdminRoute.jsx
│   │   ├── 🔒 PrivateRoute.jsx
│   │   └── 🔒 ProtectedRoute.jsx
│   ├── 📡 services/              # API services
│   │   ├── 🌐 api.js
│   │   └── 💰 razorpay.js
│   ├── 🎨 styles/                # Global styles
│   ├── 🏷️ types/                 # TypeScript types/interfaces
│   ├── 🔧 utils/                 # Utility functions
│   ├── 📱 App.jsx                # Main App component
│   ├── 🚀 main.jsx               # Entry point
│   └── 🎨 index.css              # Global CSS
├── ✅ eslint.config.js
├── 🌐 index.html
├── 📦 package.json
├── 🎨 tailwind.config.js
└── ⚡ vite.config.js
```

</details>

<details>
<summary>⚡ <strong>Backend Structure</strong> - RESTful API Server</summary>

```
Backend/
├── ⚙️ config/                    # Configuration files
│   ├── 🗄️ db.js                 # MongoDB connection
│   └── 💳 razorpay.js           # Razorpay configuration
├── 🎯 controllers/               # Route controllers
│   ├── 📦 productController.js   # Product CRUD operations
│   ├── 👤 userController.js      # User management
│   ├── 📋 orderController.js     # Order processing
│   └── 💳 paymentController.js   # Payment handling
├── 🛡️ middleware/                # Express middleware
│   └── 🔐 authMiddleware.js      # Authentication & authorization
├── 🗄️ models/                   # Database models
│   ├── 📦 Product.js            # Product schema
│   ├── 👤 User.js               # User schema
│   └── 📋 Order.js              # Order schema
├── 🛣️ routes/                   # API routes
│   ├── 📦 productRoutes.js      # Product API routes
│   ├── 👤 userRoutes.js         # User API routes
│   ├── 📋 orderRoutes.js        # Order API routes
│   └── 💳 paymentRoutes.js      # Payment API routes
├── 🔧 services/                 # Business logic
├── 🛠️ utils/                   # Utility functions
│   ├── 🔑 generateToken.js      # JWT token generation
│   └── 📁 upload.js             # File upload configuration
├── ✅ validations/              # Request validation schemas
├── 🌍 .env                      # Environment variables
├── 📦 package.json              # Dependencies & scripts
├── 🚀 index.js                  # Entry point
└── 👑 setAdmin.mjs              # Admin user setup script
```

</details>

## 🚀 Getting Started

<div align="center">

### 🛠️ **Quick Setup Guide**
*Follow these steps to get LUMINA running on your local machine*

</div>

### 📋 Prerequisites
Make sure you have the following installed on your system:

| Requirement | Version | Download Link |
|-------------|---------|---------------|
| 🟢 **Node.js** | v16+ | [Download](https://nodejs.org/) |
| 🗄️ **MongoDB** | v6.0+ | [Download](https://www.mongodb.com/) |
| 💳 **Razorpay Account** | Active | [Sign Up](https://razorpay.com/) |
| 🔧 **Git** | Latest | [Download](https://git-scm.com/) |

### 📦 Installation

<details>
<summary>📥 <strong>Step 1: Clone the Repository</strong></summary>

```bash
# Clone the repository
git clone https://github.com/yourusername/lumina-store.git

# Navigate to project directory
cd lumina-store

# Check project structure
ls -la
```

</details>

<details>
<summary>⚡ <strong>Step 2: Backend Setup</strong></summary>

```bash
# Navigate to backend directory
cd Backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env  # or use your preferred editor
```

**Environment Variables:**
```env
NODE_ENV=development
PORT=5000
MONGO_URI=mongodb://localhost:27017/lumina_ecommerce
JWT_SECRET=your_super_secret_jwt_key_here
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
```

</details>

<details>
<summary>🎨 <strong>Step 3: Frontend Setup</strong></summary>

```bash
# Navigate to frontend directory (from root)
cd Frontend

# Install dependencies
npm install

# Create environment file (if needed)
cp .env.example .env

# Edit environment variables
nano .env
```

**Frontend Environment Variables:**
```env
VITE_API_URL=http://localhost:5000/api
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
```

</details>

<details>
<summary>🗄️ <strong>Step 4: Database Setup</strong></summary>

**Option 1: Local MongoDB**
```bash
# Start MongoDB service (Linux/macOS)
sudo service mongod start

# Or for Windows
net start MongoDB

# Or using mongod directly
mongod --dbpath /path/to/your/data/directory
```

**Option 2: MongoDB Atlas (Cloud)**
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get connection string
4. Update `MONGO_URI` in your `.env` file

</details>

### 🚀 Running the Application

<details>
<summary>⚡ <strong>Development Mode</strong></summary>

**Terminal 1 - Backend:**
```bash
cd Backend
npm run dev
# Server will start at http://localhost:5000
```

**Terminal 2 - Frontend:**
```bash
cd Frontend
npm run dev
# App will start at http://localhost:5173
```

</details>

<details>
<summary>🏗️ <strong>Production Mode</strong></summary>

**Backend:**
```bash
cd Backend
npm start
```

**Frontend:**
```bash
cd Frontend
npm run build
npm run preview
```

</details>

<details>
<summary>👑 <strong>Admin Setup (Optional)</strong></summary>

```bash
# Navigate to backend directory
cd Backend

# Run admin setup script
node setAdmin.mjs

# Follow the prompts to create admin user
# Email: <EMAIL> (or your choice)
# Password: your_secure_password
```

</details>

### 🌐 Accessing the Application

| Service | URL | Description |
|---------|-----|-------------|
| 🎨 **Frontend** | `http://localhost:5173` | Main application |
| ⚡ **Backend API** | `http://localhost:5000` | REST API |
| 📚 **API Docs** | `http://localhost:5000/api-docs` | API documentation |

### ✅ Verification

After setup, verify everything is working:

1. **🔍 Check Backend**: Visit `http://localhost:5000/api/users/test`
2. **🎨 Check Frontend**: Visit `http://localhost:5173`
3. **🔐 Test Login**: Try logging in with your admin credentials
4. **📦 Test Features**: Add a product, create an order, etc.

> 💡 **Tip**: Check the browser console and terminal for any error messages if something isn't working!

## 📡 API Documentation

<div align="center">

### 🔗 **RESTful API Endpoints**
*Complete API reference for developers*

</div>

<details>
<summary>🔐 <strong>Authentication Endpoints</strong></summary>

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| 🟢 POST | `/api/users/register` | Register new user | ❌ |
| 🟢 POST | `/api/users/login` | User login | ❌ |
| 🟢 POST | `/api/users/logout` | User logout | ✅ |
| 🔵 GET | `/api/users/me` | Get current user | ✅ |
| 🟡 PUT | `/api/users/profile` | Update profile | ✅ |

</details>

<details>
<summary>📦 <strong>Product Endpoints</strong></summary>

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| 🔵 GET | `/api/products` | Get all products | ❌ |
| 🔵 GET | `/api/products/:id` | Get single product | ❌ |
| 🔵 GET | `/api/products/:id/image` | Get product image | ❌ |
| 🟢 POST | `/api/products` | Create product | 👑 Admin |
| 🟡 PUT | `/api/products/:id` | Update product | 👑 Admin |
| 🔴 DELETE | `/api/products/:id` | Delete product | 👑 Admin |

</details>

<details>
<summary>📋 <strong>Order Endpoints</strong></summary>

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| 🟢 POST | `/api/orders` | Create new order | ✅ |
| 🔵 GET | `/api/orders/myorders` | Get user orders | ✅ |
| 🔵 GET | `/api/orders/:id` | Get single order | ✅ |
| 🟡 PUT | `/api/orders/:id/pay` | Update to paid | ✅ |
| 🔵 GET | `/api/orders` | Get all orders | 👑 Admin |

</details>

<details>
<summary>💳 <strong>Payment Endpoints</strong></summary>

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| 🟢 POST | `/api/payments/create-order` | Create payment order | ✅ |
| 🟢 POST | `/api/payments/verify` | Verify payment | ✅ |

</details>

## 🚀 Deployment

<div align="center">

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🎓 Internship Details

<div align="center">

### 🏢 **Euphoria GenX Internship Program**

</div>

<details>
<summary>📊 <strong>Internship Overview</strong></summary>

| 📝 **Detail** | 📋 **Information** |
|---------------|-------------------|
| 🏢 **Organization** | [Euphoria GenX](https://euphoriagenx.com/) |
| 👨‍💻 **Position** | Full Stack Developer Intern |
| ⏱️ **Duration** | 3 Months (Intensive Program) |
| 📅 **Period** | [Start Date] - [End Date] |
| 🎯 **Project** | LUMINA E-Commerce Store |
| 🛠️ **Technologies** | MERN Stack + Razorpay Integration |
| 📋 **Status** | ✅ Successfully Completed |
| 🏆 **Certification** | ✅ Certified by Euphoria GenX |

</details>

<details>
<summary>🎯 <strong>Key Achievements</strong></summary>

- **🏗️ Full-Stack Development**: Built complete e-commerce solution from scratch
- **🔐 Security Implementation**: Implemented secure authentication and authorization
- **🎨 Modern UI/UX**: Created responsive design with Tailwind CSS and theme support
- **💳 Payment Integration**: Successfully integrated Razorpay payment gateway
- **👑 Admin Panel**: Developed comprehensive admin dashboard
- **📱 State Management**: Mastered React Context API for complex state handling
- **🛡️ Error Handling**: Implemented robust error handling and user feedback
- **📊 Database Design**: Designed efficient MongoDB schemas and relationships

</details>

<details>
<summary>📚 <strong>Skills Acquired</strong></summary>

### 🎨 **Frontend Development**
- ⚛️ React.js (Hooks, Context API, Component Design)
- 🛣️ React Router (Navigation, Protected Routes)
- 🎨 Tailwind CSS (Responsive Design, Custom Themes)
- 📱 Progressive Web App Concepts
- 🔄 State Management Patterns

### ⚡ **Backend Development**
- 🟢 Node.js (Express.js Framework)
- 🗄️ MongoDB (Database Design, Mongoose ODM)
- 🔐 JWT Authentication (Security Best Practices)
- 📁 File Upload Handling (Multer)
- 🌐 RESTful API Design

### 🛠️ **DevOps & Tools**
- 🔧 Git Version Control
- 📦 npm Package Management
- 🐛 Debugging and Testing
- 📚 API Documentation
- 🚀 Deployment Strategies

### 💼 **Professional Skills**
- 📋 Project Planning and Management
- 👥 Team Collaboration
- 📖 Technical Documentation
- 🎯 Problem-Solving Methodologies
- 📈 Performance Optimization

</details>

## 🏆 Certification

<div align="center">

### 🎖️ **Professional Recognition**

Upon successful completion of this internship project, **Euphoria GenX** awarded a **Certificate of Completion** recognizing the skills and knowledge gained during the intensive training program. 

The project exceeded all requirements and standards set by the organization, demonstrating **advanced proficiency** in full-stack web development with modern technologies.

**🎯 Certification Highlights:**
- ✅ Complete MERN Stack Proficiency
- ✅ Payment Gateway Integration
- ✅ Professional Code Quality
- ✅ Industry-Standard Practices
- ✅ Project Management Skills

</div>

## 🙏 Acknowledgments

<div align="center">

### 💝 **Special Thanks**

</div>

<details>
<summary>🏢 <strong>Organizations & Mentors</strong></summary>

- **🎓 [Euphoria GenX](https://euphoriagenx.com/)** - For the incredible opportunity, guidance, and mentorship throughout the internship program
- **👨‍🏫 Mentorship Team** - For providing technical guidance and code reviews
- **👥 Fellow Interns** - For collaboration and knowledge sharing

</details>

<details>
<summary>🛠️ <strong>Technologies & Resources</strong></summary>

- **⚛️ [React](https://reactjs.org/)** - The library that powers our frontend
- **🟢 [Node.js](https://nodejs.org/)** - JavaScript runtime for our backend
- **⚡ [Express](https://expressjs.com/)** - Fast, minimal web framework
- **🍃 [MongoDB](https://www.mongodb.com/)** - NoSQL database for flexible data storage
- **🎨 [Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **💳 [Razorpay](https://razorpay.com/)** - Secure payment gateway solution
- **📚 Stack Overflow & GitHub** - For community support and solutions

</details>

<details>
<summary>🌟 <strong>Open Source Community</strong></summary>

- **📦 NPM Community** - For the amazing packages and tools
- **🐙 GitHub** - For hosting and version control
- **📖 MDN Web Docs** - For comprehensive documentation
- **🎥 YouTube Educators** - For supplementary learning resources
- **📱 React Community** - For best practices and patterns

</details>

## 🎯 Conclusion

<div align="center">

### 🚀 **Project Impact & Future**

</div>

This internship project with **Euphoria GenX** has been an **invaluable learning experience**, providing comprehensive hands-on experience with modern web development technologies and industry best practices. 

### 🏆 **Project Achievements**

The **LUMINA E-Commerce Store** demonstrates the ability to build a **complete, production-ready web application** with features comparable to commercial e-commerce platforms:

- ✅ **Scalable Architecture** - Modular, maintainable codebase
- ✅ **Modern Technologies** - Latest MERN stack implementation
- ✅ **Security Best Practices** - JWT authentication, input validation
- ✅ **User Experience** - Responsive design, intuitive navigation
- ✅ **Business Logic** - Complete e-commerce workflow
- ✅ **Payment Integration** - Real-world payment processing

### 📈 **Skills Development**

The skills and knowledge gained during this internship have established a **strong foundation** for a career in full-stack web development:

<div align="center">

| 🎯 **Skill Category** | 📊 **Proficiency Level** | 🚀 **Application** |
|----------------------|-------------------------|-------------------|
| **Frontend Development** | ⭐⭐⭐⭐⭐ Advanced | React, Modern CSS, State Management |
| **Backend Development** | ⭐⭐⭐⭐⭐ Advanced | Node.js, Express, RESTful APIs |
| **Database Design** | ⭐⭐⭐⭐ Proficient | MongoDB, Schema Design, Optimization |
| **Project Management** | ⭐⭐⭐⭐ Proficient | Agile Methodology, Documentation |

</div>

### 🔮 **Future Enhancements**

Potential improvements and features for continued learning:

- **🔍 Advanced Search** - Elasticsearch integration
- **📊 Analytics Dashboard** - Real-time business metrics
- **🤖 AI Recommendations** - Machine learning-based product suggestions
- **📱 Mobile App** - React Native implementation
- **🌐 Multi-language Support** - Internationalization (i18n)
- **📈 Performance Optimization** - Caching, CDN integration
- **🧪 Testing Suite** - Comprehensive unit and integration tests

### 💼 **Career Readiness**

This project experience has prepared me for **professional software development** with:

- 🎯 **Industry-Standard Practices** - Code quality, documentation, version control
- 🤝 **Team Collaboration** - Communication, code reviews, project coordination
- 🛠️ **Problem-Solving Skills** - Debugging, optimization, feature implementation
- 📚 **Continuous Learning** - Staying updated with modern development trends

---

<div align="center">

### 🌟 **Thank You for Exploring LUMINA!**

**Ready to contribute or have questions?**

[![GitHub Issues](https://img.shields.io/badge/Issues-Welcome-green?style=for-the-badge&logo=github)](https://github.com/your-repo/issues)
[![Pull Requests](https://img.shields.io/badge/PRs-Welcome-blue?style=for-the-badge&logo=github)](https://github.com/your-repo/pulls)
[![Discussions](https://img.shields.io/badge/Discussions-Join_Us-purple?style=for-the-badge&logo=github)](https://github.com/your-repo/discussions)

**Connect with the developer:**

[![LinkedIn](https://img.shields.io/badge/LinkedIn-Connect-blue?style=for-the-badge&logo=linkedin)](https://www.linkedin.com/in/yashaswi-rai-real/)
[![Portfolio](https://img.shields.io/badge/Portfolio-Visit-orange?style=for-the-badge&logo=firefox)](https://yashaswirai.github.io/Web_Resume/)
[![Email](https://img.shields.io/badge/Email-Contact-red?style=for-the-badge&logo=gmail)](mailto:<EMAIL>)

**⭐ Star this repository if you found it helpful!**

</div>
